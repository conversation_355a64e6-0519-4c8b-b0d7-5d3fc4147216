import {
  ArrowUturnLeftIcon,
  CheckIcon,
  DocumentIcon,
  DocumentPlusIcon,
  EyeIcon,
  ListBulletIcon,
  PencilIcon,
  TrashIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { useContext, useEffect, useMemo, useState } from "react";
import styled from "styled-components";
import {
  defaultBorderRadius,
  lightGray,
  vscBackground,
  vscForeground,
  vscInputBorder,
  vscListActiveBackground,
} from ".";
import { IdeMessengerContext } from "../context/IdeMessenger";
import { useAppSelector } from "../redux/hooks";
import { resolveRelativePathInDir } from "core/util/ideUtils";

export interface ModifiedFile {
  filepath: string;
  type: "create" | "edit";
  toolCallId?: string;
  status?: "pending" | "accepted" | "rejected";
}

const Container = styled.div`
  background-color: ${vscBackground};
  border: 1px solid ${vscInputBorder};
  border-radius: ${defaultBorderRadius};
  margin: 8px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border-bottom: 1px solid ${vscInputBorder};
  font-size: 12px;
  font-weight: 600;
  color: ${vscForeground};
  background-color: rgba(255, 255, 255, 0.02);
`;

const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 6px;
`;

const FileList = styled.div`
  max-height: 160px;
  overflow-y: auto;
`;

const FileItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: background-color 0.2s;

  &:hover {
    background-color: ${vscListActiveBackground};
  }

  &:last-child {
    border-bottom: none;
  }
`;

const FileInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
  cursor: pointer;
`;

const FileIcon = styled.div`
  display: flex;
  align-items: center;
  width: 14px;
  height: 14px;
  flex-shrink: 0;
`;

const FileName = styled.span`
  font-size: 12px;
  color: ${vscForeground};
  font-weight: 500;
`;

const FilePath = styled.span`
  font-size: 10px;
  color: ${lightGray};
  margin-left: 6px;
`;

const FileActions = styled.div`
  display: flex;
  gap: 2px;
  align-items: center;
`;

const ActionButton = styled.button`
  background: none;
  border: none;
  color: ${lightGray};
  cursor: pointer;
  padding: 4px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;

  &:hover {
    background-color: ${vscListActiveBackground};
    color: ${vscForeground};
  }
`;

const BottomActions = styled.div`
  display: flex;
  border-top: 1px solid ${vscInputBorder};
`;

const BottomButton = styled.button<{ variant: "discard" | "keep" }>`
  flex: 1;
  padding: 8px 12px;
  border: none;
  background-color: ${(props) =>
    props.variant === "discard" ? "#f8f9fa" : "#d4edda"};
  color: ${(props) => (props.variant === "discard" ? "#6c757d" : "#155724")};
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) =>
      props.variant === "discard" ? "#e9ecef" : "#c3e6cb"};
  }

  &:first-child {
    border-bottom-left-radius: ${defaultBorderRadius};
  }

  &:last-child {
    border-bottom-right-radius: ${defaultBorderRadius};
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: ${vscForeground};
  cursor: pointer;
  padding: 3px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;

  &:hover {
    background-color: ${vscListActiveBackground};
  }
`;

interface ModifiedFilesListProps {
  onClose?: () => void;
}

interface FileWithStatus extends ModifiedFile {
  status: "pending" | "accepted" | "rejected";
}

export function ModifiedFilesList({ onClose }: ModifiedFilesListProps) {
  const ideMessenger = useContext(IdeMessengerContext);
  const history = useAppSelector((state) => state.session.history);
  // const [workspaceDirs, setWorkspaceDirs] = useState<string[]>([]);

  // 获取工作目录
  // useEffect(() => {
  //   const getWorkspaceDirs = async () => {
  //     try {
  //       const dirs = await ideMessenger.request("getWorkspaceDirs", undefined);
  //       setWorkspaceDirs(dirs);
  //     } catch (error) {
  //       console.error("Failed to get workspace directories:", error);
  //     }
  //   };
  //   getWorkspaceDirs();
  // }, [ideMessenger]);

  // 从会话历史中提取修改的文件
  const modifiedFiles = useMemo(() => {
    const files: ModifiedFile[] = [];
    const seenFiles = new Map<string, ModifiedFile>();

    history.forEach((item) => {
      if (
        item.message.role === "assistant" &&
        item.message.toolCalls &&
        item.toolCallState
      ) {
        item.message.toolCalls.forEach((toolCall) => {
          const args = item.toolCallState?.parsedArgs;
          if (!args) return;

          let filepath: string | undefined;
          let type: "create" | "edit" | undefined;

          switch (toolCall.function?.name) {
            case "builtin_create_new_file":
              filepath = args.filepath;
              type = "create";
              break;
            case "builtin_edit_existing_file":
              filepath = args.filepath;
              type = "edit";
              break;
          }

          if (filepath && type) {
            // 如果文件已经存在，更新类型（编辑优先于创建）
            const existingFile = seenFiles.get(filepath);
            if (
              !existingFile ||
              (existingFile.type === "create" && type === "edit")
            ) {
              seenFiles.set(filepath, {
                filepath,
                type,
                toolCallId: toolCall.id,
              });
            }
          }
        });
      }
    });

    return Array.from(seenFiles.values());
  }, [history]);

  const getAbsolutePath = async (relativePath: string): Promise<string> => {
    const firstUriMatch = await resolveRelativePathInDir(
      relativePath,
      ideMessenger.ide,
    );
    if (!firstUriMatch) {
      throw new Error(`${relativePath} does not exist`);
    }
    return firstUriMatch;
  };

  const handleFileClick = async (filepath: string) => {
    try {
      const fileUri = await getAbsolutePath(filepath);
      console.log("Opening file with URI:", fileUri);
      await ideMessenger.post("openFile", { path: fileUri });
    } catch (error) {
      console.error("Failed to open file:", error);
    }
  };

  const handleShowDiff = async (filepath: string) => {
    try {
      const fileUri = await getAbsolutePath(filepath);
      console.log("Opening file diff with URI:", fileUri);

      let message = history[0].message;
      if (message.role === "user" && message.timestamp) {
        // 调用 showAgentDiff 来显示文件差异
        try {
          await ideMessenger.post("showAgentDiff", {
            filepath: fileUri,
            timestamp: message.timestamp,
          });
          console.log("Showing agent diff for file:", fileUri);
        } catch (diffError) {
          console.log("Agent diff view not available, opening file normally");
          // 如果 showAgentDiff 失败，则回退到普通的文件打开
          await ideMessenger.post("openFile", { path: fileUri });
        }
      }
    } catch (error) {
      console.error("Failed to show agent diff:", error);
    }
  };

  const getFileName = (filepath: string) => {
    return filepath.split("/").pop() || filepath;
  };

  const getFileDirectory = (filepath: string) => {
    const parts = filepath.split("/");
    return parts.slice(0, -1).join("/");
  };

  const handleRevertFile = async (filepath: string) => {
    // TODO: 实现回退单个文件修改的逻辑
    console.log("Reverting file:", filepath);
    try {
      const absolutePath = getAbsolutePath(filepath);
      // 这里可以调用IDE的撤销功能或者Git revert
      // 暂时使用runCommand来执行git checkout
      await ideMessenger.post("runCommand", {
        command: `git checkout HEAD -- "${absolutePath}"`,
      });
    } catch (error) {
      console.error("Failed to revert file:", error);
    }
  };

  const handleDiscardAll = async () => {
    // TODO: 实现拒绝所有修改的逻辑
    console.log("Discarding all changes");
    try {
      // 使用git命令回退所有修改的文件
      const filepaths = modifiedFiles
        .map((f) => `"${getAbsolutePath(f.filepath)}"`)
        .join(" ");
      await ideMessenger.post("runCommand", {
        command: `git checkout HEAD -- ${filepaths}`,
      });
      onClose?.();
    } catch (error) {
      console.error("Failed to discard all changes:", error);
    }
  };

  const handleKeepAll = async () => {
    // TODO: 实现接受所有修改的逻辑
    console.log("Keeping all changes");
    onClose?.();
  };

  const getFileIcon = (type: "create" | "edit") => {
    switch (type) {
      case "create":
        return <DocumentPlusIcon className="h-3.5 w-3.5 text-green-500" />;
      case "edit":
        return <PencilIcon className="h-3.5 w-3.5 text-blue-500" />;
      default:
        return <DocumentIcon className="h-3.5 w-3.5" />;
    }
  };

  if (modifiedFiles.length === 0) {
    return null;
  }

  return (
    <Container>
      <Header>
        <HeaderLeft>
          <ListBulletIcon className="h-3.5 w-3.5" />
          <span>{modifiedFiles.length} 个文件已修改</span>
        </HeaderLeft>
        {onClose && (
          <CloseButton onClick={onClose}>
            <XMarkIcon className="h-3.5 w-3.5" />
          </CloseButton>
        )}
      </Header>
      <FileList>
        {modifiedFiles.map((file, index) => (
          <FileItem key={`${file.filepath}-${index}`}>
            <FileInfo onClick={() => handleShowDiff(file.filepath)}>
              <FileIcon>{getFileIcon(file.type)}</FileIcon>
              <div>
                <FileName>{getFileName(file.filepath)}</FileName>
                <FilePath>{getFileDirectory(file.filepath)}</FilePath>
              </div>
            </FileInfo>
            <FileActions>
              <ActionButton
                onClick={(e) => {
                  e.stopPropagation();
                  handleFileClick(file.filepath);
                }}
                title="打开文件"
              >
                <EyeIcon className="h-3.5 w-3.5" />
              </ActionButton>
              <ActionButton
                onClick={(e) => {
                  e.stopPropagation();
                  handleRevertFile(file.filepath);
                }}
                title="回退此文件的修改"
              >
                <TrashIcon className="h-3.5 w-3.5" />
              </ActionButton>
            </FileActions>
          </FileItem>
        ))}
      </FileList>
      <BottomActions>
        <BottomButton variant="discard" onClick={handleDiscardAll}>
          <ArrowUturnLeftIcon className="h-3.5 w-3.5" />
          拒绝全部
        </BottomButton>
        <BottomButton variant="keep" onClick={handleKeepAll}>
          <CheckIcon className="h-3.5 w-3.5" />
          接受全部
        </BottomButton>
      </BottomActions>
    </Container>
  );
}
